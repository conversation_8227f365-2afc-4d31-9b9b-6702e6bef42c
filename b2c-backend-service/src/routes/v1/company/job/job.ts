import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import Company from '@modules/company';
import { FastifyInstance, FastifyReply } from 'fastify';
import { FastifyRequestI } from '@interfaces/common/declaration';
import { Job } from '@schemas/company/job/job';
import { RouteParamsSchema } from '@schemas/common/common';

const jobRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/company/job/candidate', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = Job.FetchManyForCandidateQuerySchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('JOB003', queryError);
    }
    const { error: bodyError, data: bodyData } = Job.FetchManyForCandidateBodySchema.safeParse(request.body);
    console.log(bodyError, 'bodyError') 
    if (bodyError) {
      throw new AppError('JOB002', bodyError);
    }
    const result = await Company.JobModule.fetchManyForCandidate(request, queryData, bodyData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get(
    '/backend/api/v1/company/job/candidate/:id',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: paramsError, data: paramsData } = RouteParamsSchema.safeParse(request.params);
      if (paramsError) {
        throw new AppError('JOB002', paramsError);
      }
      const result = await Company.JobModule.fetchOneForCandidate(request, paramsData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.get(
    '/backend/api/v1/company/job/entity/:id',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: paramsError, data: paramsData } = RouteParamsSchema.safeParse(request.params);
      if (paramsError) {
        throw new AppError('JOB002', paramsError);
      }
      const result = await Company.JobModule.fetchOneForCandidate(request, paramsData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.post(
    '/backend/api/v1/company/job/entity-member',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = Job.FetchManyForEntityMemberQuerySchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('JOB003', queryError);
      }
      const { error: bodyError, data: bodyData } = Job.FetchManyForEntityMemberBodySchema.safeParse(request.body);
      if (bodyError) {
        throw new AppError('JOB002', bodyError);
      }
      const result = await Company.JobModule.fetchManyForEntityMember(request, queryData, bodyData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
  // fastify.post('/backend/api/v1/company/job/core', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
  //   const { error: queryError, data: queryData } = Job.CreateOneSchema.safeParse(request.query);
  //   if (queryError) {
  //     throw new AppError('JOB003', queryError);
  //   }
  //   const result = await Company.JobModule.createOne(request, queryData);
  //   reply.status(HttpStatus.CREATED).send(result);
  // });
  // fastify.patch('/backend/api/v1/company/job/core/:id', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
  //   const { data: paramsData, error: paramsError } = RouteParamsSchema.safeParse(request.params);
  //   if (paramsError) {
  //     throw new AppError('JOB010', paramsError);
  //   }
  //   const { error: bodyError, data: bodyData } = Job.UpdateOneSchema.safeParse({
  //     ...(request.body as Job.UpdateOneI),
  //     ...paramsData,
  //   });
  //   if (bodyError) {
  //     throw new AppError('JOB009', bodyError);
  //   }
  //   const result = await Company.JobModule.updateOne(request, bodyData);
  //   reply.status(HttpStatus.OK).send(result);
  // });

  fastify.post('/backend/api/v1/company/job/details', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error, data } = Job.CreateJobDetailsSchema.safeParse(request.body);
    if (error) {
      throw new AppError('JOB002', error);
    }
    const result = await Company.JobModule.createJobDetails(request, data);
    reply.status(HttpStatus.CREATED).send(result);
  });

  fastify.patch('/backend/api/v1/company/job/:id/details', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: paramsError, data: paramsData } = RouteParamsSchema.safeParse(request.params);
    if (paramsError) {
      throw new AppError('JOB010', paramsError);
    }

    const bodyWithJobId = {
      ...(request.body as Record<string, unknown>),
      jobId: paramsData.id,
    };

    const { error, data } = Job.UpdateJobDetailsSchema.safeParse(bodyWithJobId);
    if (error) {
      throw new AppError('JOB009', error);
    }

    const result = await Company.JobModule.updateJobDetails(request, data, 'draft');
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.patch('/backend/api/v1/company/job/:id/edit/details', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: paramsError, data: paramsData } = RouteParamsSchema.safeParse(request.params);
    if (paramsError) {
      throw new AppError('JOB010', paramsError);
    }

    const bodyWithJobId = {
      ...(request.body as Record<string, unknown>),
      jobId: paramsData.id,
    };

    const { error, data } = Job.UpdateJobDetailsSchema.safeParse(bodyWithJobId);
    if (error) {
      throw new AppError('JOB009', error);
    }

    const result = await Company.JobModule.updateJobDetails(request, data, 'edit');
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.patch('/backend/api/v1/company/job/:id/requirements', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: paramsError, data: paramsData } = RouteParamsSchema.safeParse(request.params);
    if (paramsError) {
      throw new AppError('JOB010', paramsError);
    }

    const bodyWithJobId = {
      ...(request.body as Record<string, unknown>),
      jobId: paramsData.id,
    };

    const { error, data } = Job.UpdateJobRequirementsSchema.safeParse(bodyWithJobId);
    if (error) {
      throw new AppError('JOB009', error);
    }

    const result = await Company.JobModule.updateJobRequirements(request, data, 'draft');
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.patch('/backend/api/v1/company/job/:id/benefits', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: paramsError, data: paramsData } = RouteParamsSchema.safeParse(request.params);
    if (paramsError) {
      throw new AppError('JOB010', paramsError);
    }

    const bodyWithJobId = {
      ...(request.body as Record<string, unknown>),
      jobId: paramsData.id,
    };

    const { error, data } = Job.UpdateJobBenefitsSchema.safeParse(bodyWithJobId);
    if (error) {
      throw new AppError('JOB009', error);
    }

    const result = await Company.JobModule.updateJobBenefits(request, data, 'draft');
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.patch('/backend/api/v1/company/job/:id/requirements-mobile', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: paramsError, data: paramsData } = RouteParamsSchema.safeParse(request.params);
    if (paramsError) {
      throw new AppError('JOB010', paramsError);
    }

    const bodyWithJobId = {
      ...(request.body as Record<string, unknown>),
      jobId: paramsData.id,
    };

    const { error, data } = Job.UpdateJobRequirementsMobileSchema.safeParse(bodyWithJobId);
    if (error) {
      throw new AppError('JOB009', error);
    }

    const result = await Company.JobModule.updateJobRequirementsMobile(request, data, 'draft');
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.patch('/backend/api/v1/company/job/:id/edit/requirements', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: paramsError, data: paramsData } = RouteParamsSchema.safeParse(request.params);
    if (paramsError) {
      throw new AppError('JOB010', paramsError);
    }

    const bodyWithJobId = {
      ...(request.body as Record<string, unknown>),
      jobId: paramsData.id,
    };

    const { error, data } = Job.UpdateJobRequirementsSchema.safeParse(bodyWithJobId);
    if (error) {
      throw new AppError('JOB009', error);
    }

    const result = await Company.JobModule.updateJobRequirements(request, data, 'edit');
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.patch('/backend/api/v1/company/job/:id/edit/benefits', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: paramsError, data: paramsData } = RouteParamsSchema.safeParse(request.params);
    if (paramsError) {
      throw new AppError('JOB010', paramsError);
    }

    const bodyWithJobId = {
      ...(request.body as Record<string, unknown>),
      jobId: paramsData.id,
    };

    const { error, data } = Job.UpdateJobBenefitsSchema.safeParse(bodyWithJobId);
    if (error) {
      throw new AppError('JOB009', error);
    }

    const result = await Company.JobModule.updateJobBenefits(request, data, 'edit');
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.patch('/backend/api/v1/company/job/:id/edit/requirements-mobile', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: paramsError, data: paramsData } = RouteParamsSchema.safeParse(request.params);
    if (paramsError) {
      throw new AppError('JOB010', paramsError);
    }

    const bodyWithJobId = {
      ...(request.body as Record<string, unknown>),
      jobId: paramsData.id,
    };

    const { error, data } = Job.UpdateJobRequirementsMobileSchema.safeParse(bodyWithJobId);
    if (error) {
      throw new AppError('JOB009', error);
    }

    const result = await Company.JobModule.updateJobRequirementsMobile(request, data, 'edit');
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.get('/backend/api/v1/company/job/draft/:id', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: paramsError, data: paramsData } = RouteParamsSchema.safeParse(request.params);
    if (paramsError) {
      throw new AppError('JOB002', paramsError);
    }
    const result = await Company.JobModule.fetchDraftJob(request, paramsData.id);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get(
    '/backend/api/v1/company/jobs/candidate/filters',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = Job.FetchFiltersForCandidateSchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('JOB003', queryError);
      }
      const result = await Company.JobModule.fetchFiltersForCandidate(request, queryData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.get(
    '/backend/api/v1/company/jobs/entity-member/filters',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = Job.FetchFiltersForCandidateSchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('JOB003', queryError);
      }
      const result = await Company.JobModule.fetchFiltersForEntityMember(request, queryData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default jobRoutes;
